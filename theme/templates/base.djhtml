{% load static tailwind_tags %}
<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <title>Django Tailwind</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    {% tailwind_css %}
</head>

<body class="bg-gray-50 text-black font-serif leading-normal tracking-normal">

    <div class="toast toast-top toast-end">
        <div class="alert alert-info">
            <span>Hello from daisyUi 👋</span>
        </div>
    </div>

<div class="container mx-auto">
    <section class="flex items-center justify-center h-screen">
        <h1 class="text-5xl">Django + Tailwind = ❤️</h1>
    </section>
</div>
</body>
</html> -->
<!doctype html>
<html lang="en" class="[scrollbar-gutter:stable]">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content="{get_csrf_token()}" />
    <title>{% block title %}{% endblock %}</title>
    <link
      rel="icon"
      href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>👋</text></svg>"
    />
    {% tailwind_css %}
    <style>
      @media print {
        @page {
          margin-top: 1.5cm;
          margin-bottom: 1.5cm;
          margin-left: 1.5cm;
          margin-right: 1.5cm;
        }
      }
    </style>
  </head>
  <body class="bg-white">
    {% block content %}{% endblock %}
  </body>
</html>
