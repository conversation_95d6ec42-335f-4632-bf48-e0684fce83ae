# fly.toml app configuration file generated for mitkins on 2025-07-24T14:41:39+10:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'mitkins'
primary_region = 'syd'
console_command = '/code/manage.py shell'

[build]

[env]
  PORT = '8000'

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

[[statics]]
  guest_path = '/code/static'
  url_prefix = '/static/'
